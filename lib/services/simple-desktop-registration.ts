/**
 * Simple Desktop Registration Service
 * 
 * KISS principle: Keep it simple & robust
 * - Registers desktop CouchDB server with VPS on app start
 * - Maintains periodic heartbeat to stay alive
 * - Handles all error cases gracefully
 * - No complex dependencies, just works!
 */

import { multiUserSessionManager } from '@/lib/auth/multi-user-session-manager';
import { getCurrentRestaurantId } from '@/lib/db/v4/utils/restaurant-id';

interface RegistrationState {
  deviceId: string | null;
  isRegistered: boolean;
  lastRegistration: Date | null;
  lastHeartbeat: Date | null;
  failureCount: number;
  error: string | null;
}

class SimpleDesktopRegistration {
  private state: RegistrationState = {
    deviceId: null,
    isRegistered: false,
    lastRegistration: null,
    lastHeartbeat: null,
    failureCount: 0,
    error: null
  };

  private registrationTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isElectron: boolean = false;
  private vpsUrl: string = '';

  constructor() {
    console.log('🖥️ [SimpleRegistration] Initializing desktop registration service...');
    
    // Detect environment
    this.isElectron = typeof window !== 'undefined' && typeof (window as any).electronAPI !== 'undefined';
    
    // Set VPS URL
    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
      this.vpsUrl = window.location.origin; // Dev: localhost:3000
    } else {
      this.vpsUrl = 'https://bistro.icu'; // Prod: bistro.icu
    }

    console.log('🖥️ [SimpleRegistration] Environment:', {
      isElectron: this.isElectron,
      vpsUrl: this.vpsUrl
    });
  }

  /**
   * Start the registration service
   * Called immediately when app starts
   */
  async start(): Promise<void> {
    console.log('🚀 [SimpleRegistration] Starting registration service...');

    if (!this.isElectron) {
      console.log('📱 [SimpleRegistration] Not a desktop app - skipping registration');
      return;
    }

    // Try to register immediately
    await this.attemptRegistration();

    // Start periodic checks (every 30 seconds initially, then less frequent)
    this.startPeriodicChecks();

    console.log('✅ [SimpleRegistration] Registration service started');
  }

  /**
   * Stop the registration service
   */
  stop(): void {
    console.log('🛑 [SimpleRegistration] Stopping registration service...');
    
    if (this.registrationTimer) {
      clearTimeout(this.registrationTimer);
      this.registrationTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearTimeout(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * Get current registration status
   */
  getStatus(): RegistrationState {
    return { ...this.state };
  }

  /**
   * Force registration attempt (for debugging)
   */
  async forceRegistration(): Promise<boolean> {
    console.log('🔧 [SimpleRegistration] Forcing registration...');
    return await this.attemptRegistration();
  }

  private async attemptRegistration(): Promise<boolean> {
    console.log('🔄 [SimpleRegistration] Attempting registration...', {
      currentState: this.state.isRegistered,
      failureCount: this.state.failureCount
    });

    try {
      // Check prerequisites
      const session = multiUserSessionManager.getActiveSession();
      const restaurantId = getCurrentRestaurantId();

      if (!session?.token) {
        console.log('⚠️ [SimpleRegistration] No active session - will retry later');
        this.scheduleRetry(10000); // Retry in 10 seconds
        return false;
      }

      if (!restaurantId) {
        console.log('⚠️ [SimpleRegistration] No restaurant ID - will retry later');
        this.scheduleRetry(10000);
        return false;
      }

      // Generate device ID if we don't have one
      if (!this.state.deviceId) {
        this.state.deviceId = `desktop-${restaurantId.replace(/[^a-zA-Z0-9]/g, '')}-${Date.now().toString().slice(-6)}`;
        console.log('🆔 [SimpleRegistration] Generated device ID:', this.state.deviceId);
      }

      // Attempt registration with VPS
      console.log('📡 [SimpleRegistration] Registering with VPS...', {
        deviceId: this.state.deviceId,
        vpsUrl: this.vpsUrl
      });

      const response = await fetch(`${this.vpsUrl}/api/sync/register-device`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceId: this.state.deviceId,
          deviceType: 'desktop',
          couchdbPort: 5984 // Assume standard CouchDB port
        })
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(`Registration failed: ${response.status} - ${error.error || response.statusText}`);
      }

      const result = await response.json();
      
      // Success!
      this.state.isRegistered = true;
      this.state.lastRegistration = new Date();
      this.state.failureCount = 0;
      this.state.error = null;

      console.log('✅ [SimpleRegistration] Registration successful!', {
        deviceId: result.deviceId,
        restaurantId: result.restaurantId
      });

      // Start heartbeat to keep alive
      this.startHeartbeat();
      
      return true;

    } catch (error) {
      this.state.failureCount++;
      this.state.error = error instanceof Error ? error.message : 'Unknown error';
      
      console.error('❌ [SimpleRegistration] Registration failed:', {
        error: this.state.error,
        failureCount: this.state.failureCount
      });

      // Schedule retry with exponential backoff
      const retryDelay = Math.min(30000, 5000 * Math.pow(2, this.state.failureCount - 1));
      this.scheduleRetry(retryDelay);
      
      return false;
    }
  }

  private startPeriodicChecks(): void {
    // If not registered, check frequently (every 30 seconds)
    // If registered, check less frequently (every 5 minutes)
    const checkInterval = this.state.isRegistered ? 5 * 60 * 1000 : 30 * 1000;
    
    this.registrationTimer = setTimeout(async () => {
      if (!this.state.isRegistered) {
        await this.attemptRegistration();
      }
      
      // Reschedule
      this.startPeriodicChecks();
    }, checkInterval);
  }

  private scheduleRetry(delay: number): void {
    if (this.registrationTimer) {
      clearTimeout(this.registrationTimer);
    }
    
    console.log(`⏰ [SimpleRegistration] Scheduling retry in ${delay}ms...`);
    
    this.registrationTimer = setTimeout(async () => {
      await this.attemptRegistration();
    }, delay);
  }

  private startHeartbeat(): void {
    // Stop any existing heartbeat
    if (this.heartbeatTimer) {
      clearTimeout(this.heartbeatTimer);
    }

    // Send heartbeat every 2 minutes
    this.heartbeatTimer = setTimeout(async () => {
      await this.sendHeartbeat();
      
      // Reschedule
      this.startHeartbeat();
    }, 2 * 60 * 1000);
  }

  private async sendHeartbeat(): Promise<void> {
    if (!this.state.isRegistered || !this.state.deviceId) {
      return;
    }

    try {
      const session = multiUserSessionManager.getActiveSession();
      
      if (!session?.token) {
        console.log('⚠️ [SimpleRegistration] No session for heartbeat - will re-register');
        this.state.isRegistered = false;
        await this.attemptRegistration();
        return;
      }

      console.log('💓 [SimpleRegistration] Sending heartbeat...');

      const response = await fetch(`${this.vpsUrl}/api/sync/heartbeat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceId: this.state.deviceId
        })
      });

      if (response.ok) {
        this.state.lastHeartbeat = new Date();
        console.log('💓 [SimpleRegistration] Heartbeat successful');
      } else {
        console.warn('⚠️ [SimpleRegistration] Heartbeat failed, will re-register');
        this.state.isRegistered = false;
        await this.attemptRegistration();
      }

    } catch (error) {
      console.warn('⚠️ [SimpleRegistration] Heartbeat error:', error);
      // Don't fail completely, just try again later
    }
  }
}

// Export singleton instance
export const simpleDesktopRegistration = new SimpleDesktopRegistration();