import type { DiscoveredPeer } from './internet-sync';

interface InternetDiscoveryConfig {
  vpsBaseUrl: string;
  authToken: string;
  deviceId: string;
  deviceType: 'desktop' | 'mobile';
}

interface DesktopServerRegistration {
  deviceId: string;
  couchdbPort: number;
  // Note: publicIP is auto-detected by VPS, not provided by client
}

// Cache for faster subsequent discoveries
interface CachedPeer extends DiscoveredPeer {
  lastVerified: Date;
  failureCount: number;
}

class PeerCache {
  private cache = new Map<string, CachedPeer>();
  private readonly CACHE_DURATION = 2 * 60 * 1000; // 2 minutes (shorter for internet)
  private readonly MAX_FAILURES = 2;

  set(peer: DiscoveredPeer): void {
    const key = `${peer.id}`;
    const existing = this.cache.get(key);
    
    this.cache.set(key, {
      ...peer,
      lastSeen: new Date(),
      lastVerified: new Date(),
      failureCount: existing?.failureCount || 0
    });
  }

  get(peerId: string): CachedPeer | null {
    const cached = this.cache.get(peerId);
    
    if (!cached) return null;
    
    // Check if cache is still valid
    const now = Date.now();
    const age = now - cached.lastVerified.getTime();
    
    if (age > this.CACHE_DURATION || cached.failureCount >= this.MAX_FAILURES) {
      this.cache.delete(peerId);
      return null;
    }
    
    return cached;
  }

  getAll(): CachedPeer[] {
    const now = Date.now();
    const valid: CachedPeer[] = [];
    
    for (const [key, peer] of this.cache.entries()) {
      const age = now - peer.lastVerified.getTime();
      
      if (age <= this.CACHE_DURATION && peer.failureCount < this.MAX_FAILURES) {
        valid.push(peer);
      } else {
        this.cache.delete(key);
      }
    }
    
    // Sort by last seen (most recent first), then by failure count
    return valid.sort((a, b) => {
      const timeDiff = b.lastSeen.getTime() - a.lastSeen.getTime();
      if (timeDiff !== 0) return timeDiff;
      return a.failureCount - b.failureCount;
    });
  }

  markFailure(peerId: string): void {
    const cached = this.cache.get(peerId);
    
    if (cached) {
      cached.failureCount++;
      if (cached.failureCount >= this.MAX_FAILURES) {
        this.cache.delete(peerId);
      }
    }
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

class InternetDiscoveryService {
  private config: InternetDiscoveryConfig | null = null;
  private cache = new PeerCache();
  private isRegistered = false;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  configure(config: InternetDiscoveryConfig): void {
    this.config = config;
  }

  async registerDesktopServer(registration: DesktopServerRegistration): Promise<boolean> {
    if (!this.config) {
      throw new Error('Internet discovery service not configured');
    }

    // 🚨 CRITICAL: Only desktop devices can register as sync servers
    if (this.config.deviceType !== 'desktop') {
      throw new Error('Only desktop devices can register as sync servers. Mobile devices are clients only.');
    }

    try {
      // Desktop registration (only option now)
      const response = await fetch(`${this.config.vpsBaseUrl}/api/sync/register-device`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceId: registration.deviceId,
          deviceType: 'desktop', // Always desktop
          couchdbPort: registration.couchdbPort
          // publicIP will be auto-detected by VPS from request headers
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Desktop registration failed: ${response.status} - ${error.error}`);
      }

      console.log('✅ Desktop sync server registered for internet access');
      this.isRegistered = true;
      this.startHeartbeat();
      return true;
    } catch (error: any) {
      console.error('❌ Desktop server registration failed:', error);
      this.isRegistered = false;
      throw error;
    }
  }

  async unregisterDevice(): Promise<void> {
    if (!this.config || !this.isRegistered) return;

    try {
      this.stopHeartbeat();
      
      const isMobile = typeof window !== 'undefined' && (window as any).Capacitor;
      
      if (isMobile) {
        const { CapacitorHttp } = await import('@capacitor/core');
        await CapacitorHttp.post({
          url: `${this.config.vpsBaseUrl}/api/sync/unregister-device`,
          headers: {
            'Authorization': `Bearer ${this.config.authToken}`,
            'Content-Type': 'application/json'
          },
          data: { deviceId: this.config.deviceId }
        });
      } else {
        await fetch(`${this.config.vpsBaseUrl}/api/sync/unregister-device`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.config.authToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ deviceId: this.config.deviceId })
        });
      }

      this.isRegistered = false;
      console.log('✅ Device unregistered from internet sync');
    } catch (error) {
      console.error('❌ Device unregistration failed:', error);
    }
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    // Send heartbeat every 60 seconds
    this.heartbeatInterval = setInterval(async () => {
      await this.sendHeartbeat();
    }, 60000);
    
    // Send immediate heartbeat
    this.sendHeartbeat();
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private async sendHeartbeat(): Promise<void> {
    if (!this.config || !this.isRegistered) return;

    try {
      const isMobile = typeof window !== 'undefined' && (window as any).Capacitor;
      
      if (isMobile) {
        const { CapacitorHttp } = await import('@capacitor/core');
        await CapacitorHttp.post({
          url: `${this.config.vpsBaseUrl}/api/sync/heartbeat`,
          headers: {
            'Authorization': `Bearer ${this.config.authToken}`,
            'Content-Type': 'application/json'
          },
          data: { deviceId: this.config.deviceId },
          connectTimeout: 5000,
          readTimeout: 5000
        });
      } else {
        await fetch(`${this.config.vpsBaseUrl}/api/sync/heartbeat`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.config.authToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ deviceId: this.config.deviceId })
        });
      }
    } catch (error) {
      console.error('❌ Heartbeat failed, attempting to re-register:', error);
      this.isRegistered = false;
      this.stopHeartbeat();
    }
  }

  async discoverPeers(): Promise<DiscoveredPeer[]> {
    if (!this.config) {
      throw new Error('Internet discovery service not configured');
    }

    console.log('🔍 Discovering internet peers...');

    // First, return any valid cached peers for immediate response
    const cachedPeers = this.cache.getAll();
    if (cachedPeers.length > 0) {
      console.log(`💾 Found ${cachedPeers.length} cached peer(s), returning immediately`);
      
      // Start background refresh but return cached results immediately
      setTimeout(() => this.refreshPeers(), 100);
      
      return cachedPeers;
    }

    // No cache, perform full discovery
    return await this.performDiscovery();
  }

  private async refreshPeers(): Promise<void> {
    console.log('🔄 Background refresh of cached peers...');
    await this.performDiscovery();
  }

  private async performDiscovery(): Promise<DiscoveredPeer[]> {
    if (!this.config) {
      throw new Error('Internet discovery service not configured');
    }

    try {
      const isMobile = typeof window !== 'undefined' && (window as any).Capacitor;
      
      if (isMobile) {
        const { CapacitorHttp } = await import('@capacitor/core');
        const response = await CapacitorHttp.get({
          url: `${this.config.vpsBaseUrl}/api/sync/discover-peers`,
          headers: {
            'Authorization': `Bearer ${this.config.authToken}`,
            'Accept': 'application/json'
          }
        });

        if (response.status !== 200) {
          throw new Error(`Discovery failed: ${response.status} - ${response.data?.error}`);
        }

        const peers = response.data.peers.map((peer: any) => ({
          ...peer,
          lastSeen: new Date(peer.lastSeen)
        }));

        // Filter out self and cache results
        const filteredPeers = peers.filter((peer: DiscoveredPeer) => peer.id !== this.config!.deviceId);
        filteredPeers.forEach(peer => this.cache.set(peer));

        console.log(`✅ Found ${filteredPeers.length} internet peer(s)`);
        return filteredPeers;
      } else {
        const response = await fetch(`${this.config.vpsBaseUrl}/api/sync/discover-peers`, {
          headers: {
            'Authorization': `Bearer ${this.config.authToken}`,
            'Accept': 'application/json'
          }
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(`Discovery failed: ${response.status} - ${error.error}`);
        }

        const data = await response.json();
        const peers = data.peers.map((peer: any) => ({
          ...peer,
          lastSeen: new Date(peer.lastSeen)
        }));

        // Filter out self and cache results
        const filteredPeers = peers.filter((peer: DiscoveredPeer) => peer.id !== this.config!.deviceId);
        filteredPeers.forEach(peer => this.cache.set(peer));

        console.log(`✅ Found ${filteredPeers.length} internet peer(s)`);
        return filteredPeers;
      }
    } catch (error: any) {
      console.error('❌ Internet peer discovery failed:', error);
      return [];
    }
  }

  markPeerFailure(peerId: string): void {
    this.cache.markFailure(peerId);
  }

  isDeviceRegistered(): boolean {
    return this.isRegistered;
  }

  isConfigured(): boolean {
    return this.config !== null;
  }

  // Export cache management functions
  getCachedPeers(): CachedPeer[] {
    return this.cache.getAll();
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheSize(): number {
    return this.cache.size();
  }
}

export const internetDiscoveryService = new InternetDiscoveryService();
export type { InternetDiscoveryConfig, DesktopServerRegistration, CachedPeer };