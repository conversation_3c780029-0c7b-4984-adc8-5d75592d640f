/**
 * Print Execution Service
 *
 * Handles actual print execution with support for:
 * - Electron silent printing
 * - Browser printing with fallbacks
 * - Print queue management
 * - Error handling and retries
 * - Print status monitoring
 */

import { printStatusMonitor } from './print-status-monitor';
import { isDevelopmentMode, getPrintExecutionDelay } from '@/lib/utils/environment';

export interface PrintJob {
  id: string;
  title: string;
  content: string;
  type: 'kitchen' | 'receipt' | 'report' | 'expo';
  printerId?: string;
  printerName?: string;
  copies?: number;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  createdAt: string;
  attempts?: number;
  maxAttempts?: number;
  status?: 'pending' | 'printing' | 'completed' | 'failed' | 'cancelled';
  error?: string;
}

export interface PrintResult {
  success: boolean;
  jobId: string;
  printed: boolean;
  printerUsed?: string;
  copies?: number;
  error?: string;
  fallbackUsed?: boolean;
  executionTime?: number;
  warning?: string;
  message?: string;
  printerStatus?: string;
}

export interface PrinterInfo {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'unknown';
  type: 'thermal' | 'inkjet' | 'laser';
  isDefault?: boolean;
  isElectronPrinter?: boolean;
}

class PrintExecutionService {
  private printQueue: PrintJob[] = [];
  private isProcessingQueue = false;
  private printers: PrinterInfo[] = [];
  private isElectron = false;
  private electronAPI: any = null;
  private offlineQueue: PrintJob[] = [];
  private isOnline = true;
  private retryIntervals: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    this.detectEnvironment();
    this.initializeService();
    this.loadOfflineQueue();
    this.setupNetworkMonitoring();
  }

  /**
   * Detect if running in Electron environment
   */
  private detectEnvironment(): void {
    // Only initialize in browser environment (not during SSR)
    if (typeof window === 'undefined') {
      console.log('🖨️ [PrintExecution] Skipping initialization during SSR');
      return;
    }
    
    this.isElectron = !!(window as any).electronAPI;
    this.electronAPI = (window as any).electronAPI;
    
    if (!this.isElectron) {
      console.warn('🖨️ [PrintExecution] Non-Electron environment detected - printing features disabled');
      return;
    }
    
    console.log(`🖨️ [PrintExecution] Environment: Electron`);
  }

  /**
   * Initialize the print execution service
   */
  private async initializeService(): Promise<void> {
    // Skip initialization during SSR or non-Electron environments
    if (typeof window === 'undefined' || !this.isElectron) {
      console.log('🖨️ [PrintExecution] Skipping service initialization (SSR or non-Electron)');
      return;
    }
    
    try {
      await this.refreshPrinters();
      this.startQueueProcessor();
      console.log('🖨️ [PrintExecution] Service initialized successfully');
    } catch (error) {
      console.error('🖨️ [PrintExecution] Failed to initialize service:', error);
    }
  }

  /**
   * Refresh available printers
   */
  async refreshPrinters(): Promise<PrinterInfo[]> {
    try {
      this.printers = [];

      if (!this.isElectron || !this.electronAPI?.printing) {
        console.warn('🖨️ [PrintExecution] Printer refresh skipped - not in Electron environment');
        return this.printers;
      }

      // Get Electron system printers
      const systemPrinters = await this.electronAPI.printing.getSystemPrinters();
      this.printers = systemPrinters.map((printer: any) => ({
        id: printer.id,
        name: printer.name,
        status: printer.status,
        type: printer.type,
        isDefault: printer.isDefault,
        isElectronPrinter: true
      }));
      console.log(`🖨️ [PrintExecution] Found ${this.printers.length} Electron printers`);

      return this.printers;
    } catch (error) {
      console.error('🖨️ [PrintExecution] Failed to refresh printers:', error);
      return this.printers;
    }
  }

  /**
   * Execute a print job immediately
   */
  async executePrint(job: PrintJob): Promise<PrintResult> {
    const startTime = Date.now();
    const jobId = job.id || this.generateJobId();
    job.id = jobId;

    console.log(`🖨️ [PrintExecution] Executing print job: ${jobId}`, {
      type: job.type,
      printer: job.printerName,
      copies: job.copies
    });

    // Track job in monitor
    printStatusMonitor.trackJob({
      id: jobId,
      title: job.title,
      type: job.type,
      printerName: job.printerName,
      priority: job.priority,
      maxAttempts: job.maxAttempts
    });

    if (!this.isElectron) {
      console.warn(`🖨️ [PrintExecution] Print job ${jobId} skipped - not in Electron environment`);
      return {
        success: false,
        jobId,
        printed: false,
        error: 'Printing not available in web environment',
        executionTime: Date.now() - startTime
      };
    }

    try {
      // Update job status
      job.status = 'printing';
      job.attempts = (job.attempts || 0) + 1;

      // Update monitor
      printStatusMonitor.updateJobStatus(jobId, 'printing');

      // Use Electron silent printing
      const result = await this.executeElectronPrint(job);

      // Update job status based on result
      job.status = result.success ? 'completed' : 'failed';
      if (!result.success) {
        job.error = result.error;
      }

      result.jobId = jobId;
      result.executionTime = Date.now() - startTime;

      // Update monitor with final status
      printStatusMonitor.updateJobStatus(
        jobId,
        result.success ? 'completed' : 'failed',
        {
          error: result.error,
          executionTime: result.executionTime,
          printerUsed: result.printerUsed
        }
      );

      console.log(`🖨️ [PrintExecution] Print job ${jobId} ${result.success ? 'completed' : 'failed'}`, result);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown print error';
      job.status = 'failed';
      job.error = errorMessage;

      const executionTime = Date.now() - startTime;

      // Update monitor with error
      printStatusMonitor.updateJobStatus(jobId, 'failed', {
        error: errorMessage,
        executionTime
      });

      console.error(`🖨️ [PrintExecution] Print job ${jobId} failed:`, error);

      return {
        success: false,
        jobId,
        printed: false,
        error: errorMessage,
        executionTime
      };
    }
  }

  /**
   * Execute print using thermal printer
   */
  private async executeElectronPrint(job: PrintJob): Promise<PrintResult> {
    try {
      const printOptions = {
        type: job.type === 'receipt' ? 'receipt' as const : 'kitchen' as const,
        content: this.prepareTextContent(job.content),
        printerName: job.printerName,
        copies: job.copies || 1
      };

      const result = await this.electronAPI.printing.thermalPrint(printOptions);
      
      if (!result.success) {
        console.error(`🖨️ [PrintExecution] Thermal print job ${job.id} failed:`, result.error);
        throw new Error(result.error || 'Unknown thermal print error');
      }
      
      return {
        success: result.success,
        jobId: job.id,
        printed: result.printed || false,
        printerUsed: result.printerUsed,
        copies: result.copies,
        error: result.error,
        fallbackUsed: false,
        message: result.message
      };

    } catch (error) {
      throw new Error(`Thermal print failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }


  /**
   * Prepare text content for thermal printing
   */
  private prepareTextContent(content: string): string {
    // Strip HTML tags and extract text content for thermal printer
    let textContent = content;
    
    // Remove HTML tags but preserve line breaks
    textContent = textContent.replace(/<br\s*\/?>/gi, '\n');
    textContent = textContent.replace(/<\/p>/gi, '\n\n');
    textContent = textContent.replace(/<[^>]*>/g, '');
    
    // Decode HTML entities
    textContent = textContent.replace(/&nbsp;/g, ' ');
    textContent = textContent.replace(/&amp;/g, '&');
    textContent = textContent.replace(/&lt;/g, '<');
    textContent = textContent.replace(/&gt;/g, '>');
    textContent = textContent.replace(/&quot;/g, '"');
    
    // Clean up extra whitespace
    textContent = textContent.replace(/\n\s*\n\s*\n/g, '\n\n'); // Max 2 consecutive newlines
    textContent = textContent.trim();
    
    return textContent;
  }

  /**
   * Add job to print queue
   */
  async queuePrint(job: PrintJob): Promise<string> {
    const jobId = job.id || this.generateJobId();
    job.id = jobId;
    job.status = 'pending';
    job.createdAt = job.createdAt || new Date().toISOString();
    job.attempts = 0;
    job.maxAttempts = job.maxAttempts || 3;

    this.printQueue.push(job);
    console.log(`🖨️ [PrintExecution] Job ${jobId} added to queue (${this.printQueue.length} jobs total)`);

    // Start processing if not already running
    if (!this.isProcessingQueue) {
      this.processQueue();
    }

    return jobId;
  }

  /**
   * Process the print queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.printQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    console.log(`🖨️ [PrintExecution] Starting queue processing (${this.printQueue.length} jobs)`);

    while (this.printQueue.length > 0) {
      // Sort by priority and creation time
      this.printQueue.sort((a, b) => {
        const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
        const aPriority = priorityOrder[a.priority || 'normal'];
        const bPriority = priorityOrder[b.priority || 'normal'];
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority; // Higher priority first
        }
        
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(); // Older first
      });

      const job = this.printQueue.shift()!;
      
      try {
        const result = await this.executePrint(job);
        
        if (!result.success && job.attempts! < job.maxAttempts!) {
          // Use enhanced retry mechanism
          this.scheduleRetry(job, job.attempts!);
        } else if (!result.success) {
          // Max retries reached, handle offline storage
          console.error(`🖨️ [PrintExecution] Job ${job.id} failed permanently, moving to offline queue`);
          job.status = 'failed';
          this.offlineQueue.push(job);
          this.saveOfflineQueue();
        }
        
      } catch (error) {
        console.error(`🖨️ [PrintExecution] Queue processing error for job ${job.id}:`, error);
      }
    }

    this.isProcessingQueue = false;
    console.log('🖨️ [PrintExecution] Queue processing completed');
  }

  /**
   * Start automatic queue processor
   */
  private startQueueProcessor(): void {
    setInterval(() => {
      if (!this.isProcessingQueue && this.printQueue.length > 0) {
        this.processQueue();
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Generate unique job ID
   */
  private generateJobId(): string {
    return `print-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Load offline queue from localStorage
   */
  private loadOfflineQueue(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem('print-offline-queue');
        if (stored) {
          this.offlineQueue = JSON.parse(stored);
          console.log(`🖨️ [PrintExecution] Loaded ${this.offlineQueue.length} jobs from offline queue`);
        }
      }
    } catch (error) {
      console.error('🖨️ [PrintExecution] Failed to load offline queue:', error);
      this.offlineQueue = [];
    }
  }

  /**
   * Save offline queue to localStorage
   */
  private saveOfflineQueue(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('print-offline-queue', JSON.stringify(this.offlineQueue));
      }
    } catch (error) {
      console.error('🖨️ [PrintExecution] Failed to save offline queue:', error);
    }
  }

  /**
   * Setup network monitoring for offline/online detection
   */
  private setupNetworkMonitoring(): void {
    if (typeof window !== 'undefined') {
      // Monitor online/offline status
      window.addEventListener('online', () => {
        console.log('🖨️ [PrintExecution] Network back online, processing offline queue');
        this.isOnline = true;
        this.processOfflineQueue();
      });

      window.addEventListener('offline', () => {
        console.log('🖨️ [PrintExecution] Network offline, switching to offline mode');
        this.isOnline = false;
      });

      this.isOnline = navigator.onLine;
    }
  }

  /**
   * Process offline queue when network comes back
   */
  private async processOfflineQueue(): Promise<void> {
    if (this.offlineQueue.length === 0) return;

    console.log(`🖨️ [PrintExecution] Processing ${this.offlineQueue.length} offline jobs`);

    const jobsToProcess = [...this.offlineQueue];
    this.offlineQueue = [];
    this.saveOfflineQueue();

    for (const job of jobsToProcess) {
      try {
        await this.queuePrint(job);
      } catch (error) {
        console.error(`🖨️ [PrintExecution] Failed to requeue offline job ${job.id}:`, error);
        // Put back in offline queue if still failing
        this.offlineQueue.push(job);
      }
    }

    this.saveOfflineQueue();
  }

  /**
   * Enhanced retry mechanism with exponential backoff
   */
  private scheduleRetry(job: PrintJob, attempt: number): void {
    const retryDelay = Math.min(1000 * Math.pow(2, attempt), 30000); // Max 30 seconds
    const retryId = `${job.id}-retry-${attempt}`;

    console.log(`🖨️ [PrintExecution] Scheduling retry for job ${job.id} in ${retryDelay}ms (attempt ${attempt})`);

    const timeoutId = setTimeout(async () => {
      this.retryIntervals.delete(retryId);

      try {
        const result = await this.executePrint(job);

        if (!result.success && job.attempts! < job.maxAttempts!) {
          this.scheduleRetry(job, attempt + 1);
        } else if (!result.success) {
          // Max retries reached, move to offline queue
          console.error(`🖨️ [PrintExecution] Job ${job.id} failed after ${job.maxAttempts} attempts, moving to offline queue`);
          job.status = 'failed';
          this.offlineQueue.push(job);
          this.saveOfflineQueue();
        }
      } catch (error) {
        console.error(`🖨️ [PrintExecution] Retry failed for job ${job.id}:`, error);
        if (attempt < (job.maxAttempts || 3)) {
          this.scheduleRetry(job, attempt + 1);
        }
      }
    }, retryDelay);

    this.retryIntervals.set(retryId, timeoutId);
  }

  /**
   * Get available printers
   */
  getPrinters(): PrinterInfo[] {
    return this.printers;
  }

  /**
   * Get print queue status
   */
  getQueueStatus(): { pending: number; processing: boolean; jobs: PrintJob[] } {
    return {
      pending: this.printQueue.length,
      processing: this.isProcessingQueue,
      jobs: [...this.printQueue]
    };
  }

  /**
   * Test thermal printer connectivity
   */
  async testPrinter(printerName: string): Promise<{ success: boolean; error?: string }> {
    if (!this.isElectron || !this.electronAPI?.printing) {
      return {
        success: false,
        error: 'Thermal printer testing not available in web environment'
      };
    }
    
    try {
      const result = await this.electronAPI.printing.testThermalPrinter(printerName);
      
      if (!result.success) {
        console.error(`🖨️ [PrintExecution] Thermal printer test failed for ${printerName}:`, result.error);
        return { 
          success: false, 
          error: result.error || 'Thermal printer test failed'
        };
      }

      console.log(`🖨️ [PrintExecution] Thermal printer test successful for ${printerName}`);
      return { 
        success: true, 
        message: result.message || 'Thermal printer test completed successfully'
      };
      
    } catch (error) {
      console.error(`🖨️ [PrintExecution] Thermal printer test exception for ${printerName}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Thermal test failed due to unexpected error'
      };
    }
  }

  /**
   * Get comprehensive queue statistics
   */
  getQueueStatistics(): {
    active: { pending: number; processing: boolean; jobs: PrintJob[] };
    offline: { count: number; jobs: PrintJob[] };
    retries: { active: number; scheduled: string[] };
    network: { online: boolean };
  } {
    return {
      active: {
        pending: this.printQueue.length,
        processing: this.isProcessingQueue,
        jobs: [...this.printQueue]
      },
      offline: {
        count: this.offlineQueue.length,
        jobs: [...this.offlineQueue]
      },
      retries: {
        active: this.retryIntervals.size,
        scheduled: Array.from(this.retryIntervals.keys())
      },
      network: {
        online: this.isOnline
      }
    };
  }

  /**
   * Clear failed jobs from offline queue
   */
  clearFailedJobs(): number {
    const beforeCount = this.offlineQueue.length;
    this.offlineQueue = this.offlineQueue.filter(job => job.status !== 'failed');
    this.saveOfflineQueue();
    const clearedCount = beforeCount - this.offlineQueue.length;
    console.log(`🖨️ [PrintExecution] Cleared ${clearedCount} failed jobs from offline queue`);
    return clearedCount;
  }

  /**
   * Cancel all pending retries
   */
  cancelAllRetries(): number {
    const retryCount = this.retryIntervals.size;
    this.retryIntervals.forEach(timeoutId => clearTimeout(timeoutId));
    this.retryIntervals.clear();
    console.log(`🖨️ [PrintExecution] Cancelled ${retryCount} pending retries`);
    return retryCount;
  }

  /**
   * Force process offline queue (manual trigger)
   */
  async forceProcessOfflineQueue(): Promise<void> {
    console.log('🖨️ [PrintExecution] Manually triggering offline queue processing');
    await this.processOfflineQueue();
  }
}

// Export singleton instance with environment check
let printExecutionServiceInstance: PrintExecutionService | null = null;

export const printExecutionService = (() => {
  if (typeof window !== 'undefined' && !printExecutionServiceInstance) {
    printExecutionServiceInstance = new PrintExecutionService();
  }
  return printExecutionServiceInstance || {
    refreshPrinters: async () => [],
    executePrint: async (job: PrintJob) => ({
      success: false,
      jobId: job.id || 'dummy',
      printed: false,
      error: 'Printing not available during SSR'
    }),
    queuePrint: async () => 'dummy',
    getPrinters: () => [],
    getQueueStatus: () => ({ pending: 0, processing: false, jobs: [] }),
    testPrinter: async () => ({ success: false, error: 'Not available during SSR' }),
    getQueueStatistics: () => ({
      active: { pending: 0, processing: false, jobs: [] },
      offline: { count: 0, jobs: [] },
      retries: { active: 0, scheduled: [] },
      network: { online: false }
    }),
    clearFailedJobs: () => 0,
    cancelAllRetries: () => 0,
    forceProcessOfflineQueue: async () => {}
  } as PrintExecutionService;
})();
