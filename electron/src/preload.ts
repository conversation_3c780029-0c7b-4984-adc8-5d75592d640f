import { contextBridge, ip<PERSON><PERSON><PERSON><PERSON>, IpcRendererEvent } from 'electron';

// Add PouchDB namespace for type definitions
declare namespace PouchDB {
  namespace Core {
    interface GetOptions {
      rev?: string;
      revs?: boolean;
      revs_info?: boolean;
      open_revs?: string[] | 'all';
      conflicts?: boolean;
      attachments?: boolean;
      [propName: string]: any;
    }
    
    interface PutDocument<Content extends {}> {
      _id?: string;
      _rev?: string;
      _deleted?: boolean;
      _attachments?: any;
      [propName: string]: any;
    }
    
    interface BulkDocsOptions {
      new_edits?: boolean;
    }
  }
  
  namespace Find {
    interface CreateIndexOptions {
      index: {
        fields: string[];
        name?: string;
        ddoc?: string;
        type?: string;
      };
    }
    
    interface FindRequest<T> {
      selector: any;
      fields?: string[];
      sort?: any[];
      limit?: number;
      skip?: number;
      use_index?: string | [string, string];
    }
  }
}

// Set the global flag for the renderer to detect Electron environment
// This is critical for proper database initialization
contextBridge.exposeInMainWorld('IS_DESKTOP_APP', true);

// Remove old activeDbPaths and beforeunload related to old DB management
// const activeDbPaths = new Set<string>();

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Send a message from renderer to main
  send: (channel: string, data: any) => {
    const validChannels = ['toMain', 'app-ready', 'trigger-action'];
    if (validChannels.includes(channel)) {
      ipcRenderer.send(channel, data);
    }
  },
  // Receive a message from main to renderer
  receive: (channel: string, func: (...args: any[]) => void) => {
    const validChannels = [
      'fromMain', 
      'app-event', 
      'system-update', 
      'db-event',
      'sync-status-updated',
      // NEW: Auto-updater events
      'update-available',
      'download-progress',
      'update-downloaded',
      'update-error'
    ];
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, (_event: IpcRendererEvent, ...args: any[]) => func(...args));
    }
  },
  // Invoke a method and get a promise response
  invoke: (channel: string, ...args: any[]) => {
    // This generic invoke is less used now that specific methods are defined below
    // but can be kept for other non-DB related IPC calls.
    const validChannels = [
      'get-app-info', 
      'perform-action', 
      'get-system-info',
      // PouchDB specific channels
      'ensure-db-opened',
      'pouchdb-get',
      'pouchdb-put',
      'pouchdb-remove',
      'pouchdb-bulk-docs',
      'pouchdb-create-index',
      'pouchdb-find',
      'pouchdb-close',
      'pouchdb-destroy',
      // NEW: Database listing channel
      'get-database-list',
      // CouchDB channels
      'get-couchdb-url',
      'get-couchdb-databases',
      // NEW: Local CouchDB sync channels
      'start-local-sync',
      'stop-local-sync',
      'get-local-sync-status',
      // NEW: Printer and USB device discovery
      'get-usb-devices',
      'get-system-printers',
      // NEW: Silent printing capabilities
      'silent-print',
      'test-printer',
      // NEW: Auto-updater actions
      'quit-and-install',
      // NEW: CouchDB port information
      'get-couchdb-port',
      // Attachment operations
      'pouchdb-put-attachment',
      'pouchdb-get-attachment',
      'pouchdb-remove-attachment',
      // Backup operations
      'backup:create',
      'backup:list-local',
      'backup:restore-local',
      'backup:get-status'
    ];
    if (validChannels.includes(channel)) {
      return ipcRenderer.invoke(channel, ...args);
    }
    return Promise.reject(new Error(`Unauthorized or unknown channel: ${channel}`));
  },

  // Get the current CouchDB port from main process
  getCouchDBPort: async (): Promise<number> => {
    return ipcRenderer.invoke('get-couchdb-port');
  },
  
  // --- NEW: Generic offline-first data access methods ---
  // These map to the corresponding methods in our offline-first data fetcher
  getPouchData: async (dbName: string, options: any = {}): Promise<any> => {
    console.log(`[preload.ts] getPouchData for ${dbName}`, options);
    try {
      if (options.id) {
        // Single document fetch
        return ipcRenderer.invoke('pouchdb-get', dbName, options.id);
      } else {
        // Collection fetch using find
        return ipcRenderer.invoke('pouchdb-find', dbName, {
          selector: options.selector || {},
          sort: options.sort,
          limit: options.limit,
          skip: options.skip,
          fields: options.fields
        });
      }
    } catch (error) {
      console.error(`[preload.ts] Error in getPouchData for ${dbName}:`, error);
      throw error;
    }
  },
  
  savePouchData: async (dbName: string, data: any): Promise<any> => {
    console.log(`[preload.ts] savePouchData for ${dbName}`, data);
    try {
      // Ensure the database exists
      await ipcRenderer.invoke('ensure-db-opened', dbName);
      
      // Handle arrays as bulk operations
      if (Array.isArray(data)) {
        return ipcRenderer.invoke('pouchdb-bulk-docs', dbName, data);
      }
      
      // Handle single document
      return ipcRenderer.invoke('pouchdb-put', dbName, data);
    } catch (error) {
      console.error(`[preload.ts] Error in savePouchData for ${dbName}:`, error);
      throw error;
    }
  },
  
  findPouchData: async (dbName: string, query: any = {}): Promise<any> => {
    console.log(`[preload.ts] findPouchData for ${dbName}`, query);
    try {
      // Ensure the database exists
      await ipcRenderer.invoke('ensure-db-opened', dbName);
      
      // Convert query params to PouchDB find query
      const selector: Record<string, any> = {};
      
      // Process query parameters into a selector
      Object.entries(query).forEach(([key, value]) => {
        if (value === undefined || value === null) return;
        
        // Handle special query parameters
        if (key === 'sort' || key === 'limit' || key === 'skip' || key === 'fields') {
          return; // These are handled separately
        }
        
        // Convert string "true"/"false" to boolean
        if (value === 'true') selector[key] = true;
        else if (value === 'false') selector[key] = false;
        else selector[key] = value;
      });
      
      // Build the find request
      const findRequest: PouchDB.Find.FindRequest<any> = {
        selector: Object.keys(selector).length > 0 ? selector : { _id: { $gt: null } },
        sort: query.sort ? JSON.parse(query.sort) : undefined,
        limit: query.limit ? parseInt(query.limit) : undefined,
        skip: query.skip ? parseInt(query.skip) : undefined,
        fields: query.fields ? query.fields.split(',') : undefined
      };
      
      return ipcRenderer.invoke('pouchdb-find', dbName, findRequest);
    } catch (error) {
      console.error(`[preload.ts] Error in findPouchData for ${dbName}:`, error);
      throw error;
    }
  },
  
  // --- PouchDB Operations via IPC --- 
  database: {
    ensureDbOpened: async (dbIdentifier: string): Promise<any> => {
      console.log(`[preload.ts] Invoking 'ensure-db-opened' for database: ${dbIdentifier}`);
      try {
        const result = await ipcRenderer.invoke('ensure-db-opened', dbIdentifier);
        console.log(`[preload.ts] Result from 'ensure-db-opened': `, result);
        return result;
      } catch (error) {
        console.error(`[preload.ts] Error in 'ensure-db-opened' for ${dbIdentifier}:`, error);
        throw error;
      }
    },
    get: async (dbIdentifier: string, docId: string, options?: PouchDB.Core.GetOptions): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-get', dbIdentifier, docId, options);
    },
    put: async (dbIdentifier: string, doc: PouchDB.Core.PutDocument<any>): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-put', dbIdentifier, doc);
    },
    remove: async (dbIdentifier: string, docId: string, rev: string): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-remove', dbIdentifier, docId, rev);
    },
    bulkDocs: async (dbIdentifier: string, docsParam: Array<any> | { docs: Array<any> }, options?: PouchDB.Core.BulkDocsOptions): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-bulk-docs', dbIdentifier, docsParam, options);
    },
    createIndex: async (dbIdentifier: string, indexDefinition: PouchDB.Find.CreateIndexOptions): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-create-index', dbIdentifier, indexDefinition);
    },
    find: async (dbIdentifier: string, findRequest: PouchDB.Find.FindRequest<any>): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-find', dbIdentifier, findRequest);
    },
    close: async (dbIdentifier: string): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-close', dbIdentifier);
    },
    destroy: async (dbIdentifier: string): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-destroy', dbIdentifier);
    },
    // NEW: Local sync with CouchDB methods
    startLocalSync: async (dbIdentifier: string): Promise<any> => {
      return ipcRenderer.invoke('start-local-sync', dbIdentifier);
    },
    stopLocalSync: async (dbIdentifier: string): Promise<any> => {
      return ipcRenderer.invoke('stop-local-sync', dbIdentifier);
    },
    getLocalSyncStatus: async (): Promise<any> => {
      return ipcRenderer.invoke('get-local-sync-status');
    },
    // Clear cached database instances (for account switching)
    clearCache: async (restaurantIdPattern?: string): Promise<any> => {
      console.log(`[preload.ts] Invoking 'database:clear-cache'${restaurantIdPattern ? ` for pattern: ${restaurantIdPattern}` : ''}`);
      return ipcRenderer.invoke('database:clear-cache', restaurantIdPattern);
    },
    // Attachment operations
    putAttachment: async (dbIdentifier: string, docId: string, filename: string, data: string, contentType: string): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-put-attachment', dbIdentifier, docId, filename, data, contentType);
    },
    getAttachment: async (dbIdentifier: string, docId: string, filename: string): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-get-attachment', dbIdentifier, docId, filename);
    },
    removeAttachment: async (dbIdentifier: string, docId: string, filename: string): Promise<any> => {
      return ipcRenderer.invoke('pouchdb-remove-attachment', dbIdentifier, docId, filename);
    }
  },
  
  // --- CouchDB Operations via IPC ---
  couchdb: {
    // Get the URL of the running CouchDB server
    getUrl: async (): Promise<any> => {
      return ipcRenderer.invoke('get-couchdb-url');
    },
    // Get list of CouchDB databases
    getDatabases: async (): Promise<any> => {
      return ipcRenderer.invoke('get-couchdb-databases');
    }
  },

  // --- Auto-updater Operations ---
  // Listen for update available events
  onUpdateAvailable: (callback: (info: any) => void) => {
    ipcRenderer.on('update-available', (_event: IpcRendererEvent, info: any) => callback(info));
  },
  
  // Listen for download progress events
  onDownloadProgress: (callback: (progress: any) => void) => {
    ipcRenderer.on('download-progress', (_event: IpcRendererEvent, progress: any) => callback(progress));
  },
  
  // Listen for update downloaded events
  onUpdateDownloaded: (callback: (info: any) => void) => {
    ipcRenderer.on('update-downloaded', (_event: IpcRendererEvent, info: any) => callback(info));
  },
  
  // Listen for update error events
  onUpdateError: (callback: (error: any) => void) => {
    ipcRenderer.on('update-error', (_event: IpcRendererEvent, error: any) => callback(error));
  },
  
  // Quit and install update
  quitAndInstall: async (): Promise<void> => {
    return ipcRenderer.invoke('quit-and-install');
  },

  // 🖨️ Thermal Printing API
  printing: {
    // Get system printers
    getSystemPrinters: async (): Promise<any[]> => {
      return ipcRenderer.invoke('get-system-printers');
    },

    // Universal print (works with ANY printer - thermal, inkjet, laser, PDF)
    universalPrint: async (options: {
      type: 'receipt' | 'kitchen';
      content: string;
      printerName?: string;
      copies?: number;
    }): Promise<{
      success: boolean;
      printed?: boolean;
      printerUsed?: string;
      copies?: number;
      error?: string;
      method?: string;
    }> => {
      return ipcRenderer.invoke('universal-print', options);
    },

    // Legacy thermal print (now uses universal printing)
    thermalPrint: async (options: {
      type: 'receipt' | 'kitchen';
      content: string;
      printerName?: string;
      copies?: number;
    }): Promise<{
      success: boolean;
      printed?: boolean;
      printerUsed?: string;
      copies?: number;
      error?: string;
      method?: string;
    }> => {
      return ipcRenderer.invoke('universal-print', options);
    },

    // Test thermal printer
    testThermalPrinter: async (printerName: string): Promise<{
      success: boolean;
      available: boolean;
      printerName?: string;
      testPrinted?: boolean;
      error?: string;
    }> => {
      return ipcRenderer.invoke('test-thermal-printer', printerName);
    },

    // WebContents print (native Electron printing)
    webContentsPrint: async (options: {
      deviceName: string;
      silent?: boolean;
      printBackground?: boolean;
      content: string;
      copies?: number;
    }): Promise<{
      success: boolean;
      error?: string;
      method?: string;
    }> => {
      return ipcRenderer.invoke('webcontents-print', options);
    }
  }

});