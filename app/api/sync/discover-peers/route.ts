import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import clientPromise from '@/lib/mongodb';

// Dynamic route - needs database access
export const dynamic = 'force-dynamic';


// Simple rate limiting (in production, use Redis or external service)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX = 30; // 30 requests per minute

function checkRateLimit(identifier: string): boolean {
  const now = Date.now();
  const current = rateLimitMap.get(identifier);
  
  if (!current || now > current.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }
  
  if (current.count >= RATE_LIMIT_MAX) {
    return false;
  }
  
  current.count++;
  return true;
}

export async function GET(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = verifyToken(token);
    
    if (!decoded || !decoded.restaurantId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Rate limiting per restaurant
    if (!checkRateLimit(decoded.restaurantId)) {
      return NextResponse.json({ 
        error: 'Rate limit exceeded - too many discovery requests' 
      }, { status: 429 });
    }

    // Connect to MongoDB for device registry (VPS orchestrator database)
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const devicesCollection = db.collection('device_registry');

    // Increase timeout to 15 minutes to handle network hiccups better
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

    try {
      // Query MongoDB for active desktop devices in same restaurant
      const peers = await devicesCollection.find({
        restaurantId: decoded.restaurantId,
        deviceType: 'desktop', // Only desktops expose CouchDB servers
        status: 'active',
        lastHeartbeat: { $gte: fifteenMinutesAgo }
      }).toArray();

      // Format peers for mobile client consumption - only internet-routable addresses
      const formattedPeers = peers.map(peer => ({
        id: peer.deviceId,
        deviceType: 'desktop', // Always desktop (only servers register)
        ipAddress: peer.publicIP, // 🌐 PUBLIC IP for internet access
        couchdbPort: peer.couchdbPort || 5984,
        hostname: `desktop-${peer.deviceId?.substring(0, 8) || 'unknown'}`,
        platform: 'desktop',
        lastSeen: peer.lastHeartbeat || peer.registeredAt,
        registeredAt: peer.registeredAt
      }));

    return NextResponse.json({
      peers: formattedPeers,
      restaurantId: decoded.restaurantId,
      count: formattedPeers.length
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

    } catch (error) {
      console.error('[API Sync Discover Peers] Error:', error);
      return NextResponse.json({ 
        error: 'Peer discovery failed' 
      }, { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      });
    }
  } catch (error) {
    console.error('[API Sync Discover Peers] Outer error:', error);
    return NextResponse.json({ 
      error: 'Server error' 
    }, { status: 500 });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}