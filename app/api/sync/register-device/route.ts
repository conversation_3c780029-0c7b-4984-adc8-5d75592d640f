import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import clientPromise from '@/lib/mongodb';

// Dynamic route - needs database access
export const dynamic = 'force-dynamic';

/**
 * Check if an IP address is public (internet-routable)
 * Returns false for private/local IPs that can't be reached over internet
 */
function isPublicIP(ip: string): boolean {
  if (ip === 'unknown' || !ip) return false;
  
  // Private IP ranges (RFC 1918)
  const privateRanges = [
    /^127\./, // Loopback
    /^10\./, // Class A private
    /^172\.(1[6-9]|2\d|3[01])\./, // Class B private  
    /^192\.168\./, // Class C private
    /^169\.254\./, // Link-local
    /^::1$/, // IPv6 loopback
    /^fe80:/, // IPv6 link-local
    /^fc00:/, // IPv6 unique local
  ];
  
  return !privateRanges.some(range => range.test(ip));
}


export async function POST(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = verifyToken(token);
    
    if (!decoded || !decoded.restaurantId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await req.json();
    const { deviceId, deviceType, couchdbPort } = body;

    if (!deviceId || !deviceType) {
      return NextResponse.json({ 
        error: 'deviceId and deviceType are required' 
      }, { status: 400 });
    }

    // 🚨 CRITICAL: Only desktop devices can register for internet sync
    // Mobile devices are clients only - they discover and connect to desktops
    if (deviceType !== 'desktop') {
      return NextResponse.json({ 
        error: 'Only desktop devices can register as sync servers. Mobile devices are clients only.' 
      }, { status: 400 });
    }

    if (!couchdbPort) {
      return NextResponse.json({ 
        error: 'couchdbPort is required for desktop sync servers' 
      }, { status: 400 });
    }

    // 🌐 AUTO-DETECT PUBLIC IP for internet sync
    let clientIP = req.headers.get('x-forwarded-for')?.split(',')[0]?.trim() || 
                   req.headers.get('x-real-ip') || 
                   req.headers.get('cf-connecting-ip') || 
                   'unknown';

    // 🔧 DEVELOPMENT FIX: Handle localhost development environment
    if (clientIP === 'unknown' || clientIP === '::1' || clientIP === '127.0.0.1') {
      // In development, simulate a public IP for testing
      if (process.env.NODE_ENV === 'development' || req.headers.get('host')?.includes('localhost')) {
        clientIP = '************'; // Simulate public IP for development
        console.log('🧪 [DEBUG] Development mode - using simulated public IP:', clientIP);
      }
    }

    // Validate we got a real public IP (not private/local)
    if (!isPublicIP(clientIP)) {
      return NextResponse.json({ 
        error: `Cannot register with non-public IP: ${clientIP}. Desktop must be accessible over internet for cross-device sync.` 
      }, { status: 400 });
    }

    // Connect to MongoDB for device registry (VPS orchestrator database)
    const mongoClient = await clientPromise;
    const db = mongoClient.db('resto');
    const devicesCollection = db.collection('device_registry');

    const now = new Date();
    const deviceRecord = {
      deviceId,
      deviceType: 'desktop', // Always desktop (validated above)
      restaurantId: decoded.restaurantId,
      publicIP: clientIP, // 🌐 PUBLIC IP for internet access
      couchdbPort,
      status: 'active',
      registeredAt: now,
      lastHeartbeat: now,
      capabilities: ['couchdb_server', 'pos_system', 'internet_sync'],
      metadata: {
        userAgent: req.headers.get('user-agent') || 'unknown',
        registrationSource: 'api',
        detectedFrom: req.headers.get('x-forwarded-for') ? 'proxy' : 'direct'
      }
    };

    try {
      // Upsert device record in MongoDB (update if exists, insert if new)
      await devicesCollection.replaceOne(
        { deviceId, restaurantId: decoded.restaurantId },
        deviceRecord,
        { upsert: true }
      );
    } catch (error) {
      console.error('Device registration failed:', error);
      return NextResponse.json({ 
        error: 'Failed to register device' 
      }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Device registered successfully',
      deviceId,
      restaurantId: decoded.restaurantId
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[API Sync Register Device] Error:', error);
    return NextResponse.json({ 
      error: 'Device registration failed' 
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}